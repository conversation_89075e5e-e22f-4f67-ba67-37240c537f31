# 团子 (<PERSON><PERSON><PERSON>) - 团建活动集成APP

*我们团，你们玩*

---

## 1. 项目简介

**团子**是一个集成的手机APP，旨在解决团队活动（如公司团建、班级活动、朋友聚会）中可能遇到的所有事务。其核心是高度可定制的**"环节" (Events/Flow) 系统**，允许主持人像编排剧本一样，将不同的游戏、讨论、投票等活动组合成一个完整的、可复用的团建流程。

## 2. 项目状态 (截至 2025年7月15日)

**当前阶段**: **阶段二：功能拓展** (中后期)

我们已经完成了MVP的开发、第一阶段的架构重构，以及核心功能的稳定性改进。应用目前拥有一个稳定、可扩展的框架，并成功集成了完整的核心玩法。**最新完成了预约房间功能和日历系统的开发，以及UI/UX的优化**

-   **已完成**:
    -   ✅ **核心架构**: 基于Django Channels和React Native的实时通信架构
    -   ✅ **用户系统**: 支持用户注册和基于JWT的登录认证
    -   ✅ **房间系统**: 支持创建房间、加入房间，并以"环节"为核心驱动流程
    -   ✅ **环节设计器 (V1)**: 支持创建环节模板、添加环节步骤（你画我猜/自由聊天）、删除步骤
    -   ✅ **环节设计器 (V2)**: 完善环节设计器，增加步骤的编辑和拖拽排序功能
    -   ✅ **你画我猜游戏**: 完整的绘图、猜词、计分、多轮游戏逻辑，包含性能优化和错误处理
    -   ✅ **自由讨论功能**: 实时聊天系统，支持权限管理的操作面板
    -   ✅ **权限管理系统**: 基于角色的操作权限控制（房主/管理员/参与者）
    -   ✅ **WebSocket连接优化**: 自动重连、错误处理、连接状态管理
    -   ✅ **类型安全改进**: 完善的TypeScript类型定义和接口规范
    -   ✅ **订阅模式系统**: 完整的三级订阅体系（Free/Pro/Max），包含房间限制和功能限制
    -   ✅ **订阅前端集成**: 完整的订阅管理界面、权限交互和开发调试功能
    -   ✅ **预约房间功能**: 支持预约未来的房间，包含日期时间选择和持续时间设置
    -   ✅ **日历系统**: 完整的日历视图，显示已预约的房间和可用时间段
    -   ✅ **测试系统**: 完整的单元测试和集成测试

-   **当前重点 / 下一步工作**:
    -   **[PLANNED]** 集成真实支付系统（微信支付、支付宝等）
    -   **[PLANNED]** 开发订阅历史和自动续费管理
    -   **[PLANNED]** 性能监控和优化

## 3. 技术栈

-   **后端**: Python, Django, Django REST Framework, Django Channels, Daphne (ASGI Server)
-   **前端**: React, React Native, TypeScript
-   **核心库**: React Navigation, react-native-svg, react-native-gesture-handler
-   **数据库**: SQLite (开发), PostgreSQL (生产)

## 4. 快速开始 (一站式启动)

为了简化开发流程，我们配置了自动化脚本。

1.  **配置IP地址 (仅在更换网络时需要)**:
    ```bash
    cd TuanziApp
    npm run config:ip
    ```

2.  **启动前端metro服务**:
    在一个终端窗口中，运行以下命令来启动前端Metro打包器。
    ```bash
    cd TuanziApp
    npm run start
    ```

3. **启动后端服务**:
    在一个终端窗口中，运行以下命令来启动后端服务器。
    ```bash
    cd Tuanzi
    source venv/bin/activate && daphne -b 0.0.0.0 -p 8000 Tuanzi_Backend.asgi:application
    ```

4. **启动房间监测服务**:
    在一个终端窗口中，运行以下命令来启动房间定时监测服务。
    ```bash
    cd Tuanzi
    python manage.py start_room_monitoring --interval 60 --verbose
    ```

5.  **编译并运行App**:
    在**另一个**终端窗口中，运行：
    ```bash
    cd TuanziApp
    npm run android
    ```

## 5. 核心功能特性

### 🎮 游戏系统
- **你画我猜**: 实时绘图、词汇猜测、计分系统
- **自由讨论**: 实时聊天、权限管理操作面板
- **付费环节**: 暂停环节、发言环节、投票环节、问答环节、自定义环节
- **可扩展架构**: 支持快速添加新游戏类型

### 💰 订阅模式系统
- **Free版本**:
  - 房间限制：2小时，10人
  - 仅支持基础环节（你画我猜、自由讨论）
- **Pro版本**:
  - 房间限制：24小时，500人
  - 支持所有高级环节类型
- **Max版本**:
  - 房间限制：72小时，2000人（实际无限制）
  - 完整功能访问权限

### 🎛️ 订阅管理功能
- **订阅状态展示**: 主页和专门管理页面显示当前订阅等级
- **权限交互**: 智能的功能限制和升级引导
- **开发调试**: 开发阶段可快速切换权限等级进行测试
- **升级流程**: 完整的订阅升级用户体验（支付集成预留）

### 🔧 技术特性
- **实时通信**: 基于WebSocket的低延迟通信
- **自动重连**: 网络断开自动重连机制
- **性能优化**: 绘图节流、消息批处理
- **类型安全**: 完整的TypeScript类型定义
- **错误处理**: 全面的错误捕获和用户友好提示
- **订阅验证**: 前后端双重验证，确保功能限制的安全性

### 👥 权限管理
- **房主**: 完整的房间控制权限
- **管理员**: 预留的中级权限角色
- **参与者**: 基础的游戏参与权限

## 6. 详细文档

-   [后端设置与架构 (Tuanzi_Backend/README.md)](./Tuanzi_Backend/README.md)
-   [前端设置与架构 (TuanziApp/README.md)](./TuanziApp/README.md)
-   [模块扩展指南 (CONTRIBUTING.md)](./CONTRIBUTING.md)

## 7. 开发团队

-   **开发者A (gellar)**: 负责项目架构、核心后端功能
-   **开发者B**: 负责用户界面、核心游戏功能
-   **设计师**: 提供美术意见和UI/UX指导

---

*最后更新: 2025年7月15日*

## 8. 最新更新 - 预约房间功能与日历系统

### 🎉 预约房间功能全面上线

我们成功实现了预约房间功能和日历系统，为用户提供了更灵活的活动安排方式：

#### 预约房间核心功能
- **日期时间选择**: 直观的日期和时间选择器，支持未来日期预约
- **持续时间设置**: 可自定义活动持续时间，从1小时到24小时不等
- **模板选择**: 优化的模板选择流程，支持系统模板和用户自定义模板
- **预览功能**: 预约前可预览活动环节和详细信息
- **表单验证**: 完整的输入验证，确保预约信息的有效性

#### 日历系统实现
- **月视图**: 直观的月历视图，显示已预约的活动和可用时间段
- **日视图**: 详细的日视图，显示当天所有预约的详细信息
- **交互优化**: 点击日期可直接跳转到预约界面，预填选中的日期
- **状态标识**: 使用颜色区分不同状态的日期（已过期、已预约、今天）

#### 用户体验优化
- **界面流程**: 优化的用户界面流程，减少操作步骤
- **表单状态保持**: 在选择模板过程中保持已填写的表单信息
- **错误处理**: 友好的错误提示和处理机制
- **加载状态**: 清晰的加载状态指示，提升用户体验

#### 技术实现
- **组件复用**: 高效复用现有的模板选择组件，减少代码重复
- **状态管理**: 优化的状态管理方案，确保表单数据的一致性
- **类型安全**: 完整的TypeScript类型定义，减少运行时错误
- **响应式设计**: 适配不同屏幕尺寸的响应式界面

详细报告请参见：
- [CALENDAR_SYSTEM_IMPLEMENTATION.md](./doc/CALENDAR_SYSTEM_IMPLEMENTATION.md)
- [SCHEDULE_ROOM_FEATURE_REPORT.md](./doc/SCHEDULE_ROOM_FEATURE_REPORT.md)

## 9. 历史更新 - 订阅系统完整集成

### 🎉 订阅系统全面完成

我们成功实现了从后端到前端的完整订阅系统，为团子APP的盈利模式提供了完整的技术解决方案：

#### 后端核心实现
- **三级订阅体系**: Free/Pro/Max完整支持
- **JWT增强**: 载荷包含用户订阅等级信息
- **房间限制**: 基于订阅等级的人数和时长限制
- **付费环节**: 5种高级环节类型（暂停、发言、投票、问答、自定义）
- **安全验证**: 前后端双重验证确保限制不被绕过

#### 前端集成实现
- **订阅管理界面**: 完整的订阅状态展示和管理页面
- **权限交互**: 智能的功能限制和升级引导体验
- **开发调试**: 便捷的权限等级切换功能（开发阶段）
- **用户体验**: 现代化的UI设计和流畅的交互流程

详细报告请参见：
- [SUBSCRIPTION_SYSTEM_TEST_REPORT.md](./doc/SUBSCRIPTION_SYSTEM_TEST_REPORT.md)
- [SUBSCRIPTION_FRONTEND_INTEGRATION_REPORT.md](./doc/SUBSCRIPTION_FRONTEND_INTEGRATION_REPORT.md)
