"""
房间-模板-环节系统重构测试

测试新的RoomEventStep模型和相关功能：
- RoomEventStep模型的创建和管理
- 房间环节的独立性
- 动态插入和删除环节的API
- 环节推进逻辑的正确性
"""

import pytest
from datetime import datetime, timedelta
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.db import transaction
from rest_framework.test import APITestCase
from rest_framework import status

from core.models import (
    User, Room, RoomEventStep, RoomParticipant, RoomState, TemplateManager
)
from events.models import EventTemplate, EventStep
from core.utils import advance_to_next_step_sync

User = get_user_model()


class RoomEventStepModelTest(TestCase):
    """测试RoomEventStep模型的基本功能"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        self.template = EventTemplate.objects.create(
            name='测试模板',
            description='用于测试的模板',
            creator=self.user
        )
        
        # 创建模板步骤
        EventStep.objects.create(
            template=self.template,
            name='第一个环节',
            order=1,
            step_type=EventStep.STEP_FREE_CHAT,
            duration=300
        )
        
        EventStep.objects.create(
            template=self.template,
            name='第二个环节',
            order=2,
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=600
        )
        
        self.room = Room.objects.create(
            room_code='TEST01',
            host=self.user,
            event_template=self.template,
            status=RoomState.READY
        )
    
    def test_room_event_step_creation(self):
        """测试RoomEventStep的创建"""
        step = RoomEventStep.objects.create(
            room=self.room,
            order=1,
            name='测试环节',
            step_type=EventStep.STEP_FREE_CHAT,
            duration=300,
            configuration={'test': 'value'}
        )
        
        self.assertEqual(step.room, self.room)
        self.assertEqual(step.order, 1)
        self.assertEqual(step.name, '测试环节')
        self.assertEqual(step.step_type, EventStep.STEP_FREE_CHAT)
        self.assertEqual(step.duration, 300)
        self.assertEqual(step.configuration, {'test': 'value'})
        self.assertFalse(step.is_completed)
        self.assertIsNone(step.completed_at)
    
    def test_template_steps_copy_to_room(self):
        """测试模板步骤复制到房间"""
        # 复制模板步骤到房间
        TemplateManager.copy_template_steps_to_room(self.template, 'user', self.room)
        
        # 验证房间环节已创建
        room_steps = self.room.event_steps.all().order_by('order')
        self.assertEqual(room_steps.count(), 2)
        
        # 验证第一个环节
        step1 = room_steps[0]
        self.assertEqual(step1.order, 1)
        self.assertEqual(step1.name, '第一个环节')
        self.assertEqual(step1.step_type, EventStep.STEP_FREE_CHAT)
        self.assertEqual(step1.duration, 300)
        
        # 验证第二个环节
        step2 = room_steps[1]
        self.assertEqual(step2.order, 2)
        self.assertEqual(step2.name, '第二个环节')
        self.assertEqual(step2.step_type, EventStep.STEP_GAME_PICTIONARY)
        self.assertEqual(step2.duration, 600)
    
    def test_room_step_independence(self):
        """测试房间环节的独立性"""
        # 创建两个房间使用同一个模板
        room1 = Room.objects.create(
            room_code='ROOM01',
            host=self.user,
            event_template=self.template,
            status=RoomState.READY
        )
        
        room2 = Room.objects.create(
            room_code='ROOM02',
            host=self.user,
            event_template=self.template,
            status=RoomState.READY
        )
        
        # 复制模板步骤到两个房间
        TemplateManager.copy_template_steps_to_room(self.template, 'user', room1)
        TemplateManager.copy_template_steps_to_room(self.template, 'user', room2)
        
        # 修改房间1的环节
        room1_step = room1.event_steps.first()
        room1_step.name = '修改后的环节名称'
        room1_step.duration = 500
        room1_step.save()
        
        # 验证房间2的环节没有受到影响
        room2_step = room2.event_steps.first()
        self.assertEqual(room2_step.name, '第一个环节')
        self.assertEqual(room2_step.duration, 300)
        
        # 验证原始模板也没有受到影响
        template_step = self.template.steps.first()
        self.assertEqual(template_step.name, '第一个环节')
        self.assertEqual(template_step.duration, 300)


class RoomEventStepAdvancementTest(TestCase):
    """测试房间环节推进逻辑"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_FREE
        )
        
        self.room = Room.objects.create(
            room_code='TEST01',
            host=self.user,
            status=RoomState.READY
        )
        
        # 创建房间环节
        RoomEventStep.objects.create(
            room=self.room,
            order=1,
            name='第一个环节',
            step_type=EventStep.STEP_FREE_CHAT,
            duration=300
        )
        
        RoomEventStep.objects.create(
            room=self.room,
            order=2,
            name='第二个环节',
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=600
        )
        
        RoomEventStep.objects.create(
            room=self.room,
            order=3,
            name='第三个环节',
            step_type=EventStep.STEP_FREE_CHAT,
            duration=300
        )
    
    def test_advance_to_next_step(self):
        """测试推进到下一个环节"""
        # 初始状态
        self.assertEqual(self.room.current_step_order, 0)
        self.assertIsNone(self.room.current_event_step)
        
        # 推进到第一个环节
        next_step = self.room.advance_to_next_event_step()
        self.assertIsNotNone(next_step)
        self.assertEqual(next_step.order, 1)
        self.assertEqual(next_step.name, '第一个环节')
        self.assertEqual(self.room.current_step_order, 1)
        self.assertEqual(self.room.current_event_step, next_step)
        self.assertEqual(self.room.status, RoomState.IN_PROGRESS)
        
        # 推进到第二个环节
        next_step = self.room.advance_to_next_event_step()
        self.assertIsNotNone(next_step)
        self.assertEqual(next_step.order, 2)
        self.assertEqual(next_step.name, '第二个环节')
        self.assertEqual(self.room.current_step_order, 2)
        
        # 验证第一个环节已标记为完成
        first_step = self.room.event_steps.get(order=1)
        self.assertTrue(first_step.is_completed)
        self.assertIsNotNone(first_step.completed_at)
        
        # 推进到第三个环节
        next_step = self.room.advance_to_next_event_step()
        self.assertIsNotNone(next_step)
        self.assertEqual(next_step.order, 3)
        
        # 尝试推进到下一个环节（应该没有更多环节）
        next_step = self.room.advance_to_next_event_step()
        self.assertIsNone(next_step)
        self.assertEqual(self.room.status, RoomState.READY)
        self.assertIsNone(self.room.current_event_step)
        
        # 验证第三个环节已标记为完成
        third_step = self.room.event_steps.get(order=3)
        self.assertTrue(third_step.is_completed)
    
    def test_advance_to_next_step_sync_function(self):
        """测试同步版本的推进函数"""
        # 使用utils中的函数推进环节
        step_info = advance_to_next_step_sync(self.room)
        
        self.assertIsNotNone(step_info)
        self.assertEqual(step_info.order, 1)
        self.assertEqual(step_info.name, '第一个环节')
        self.assertEqual(step_info.step_type, EventStep.STEP_FREE_CHAT)
        self.assertEqual(step_info.duration, 300)


class RoomEventStepAPITest(APITestCase):
    """测试房间环节相关的API端点"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            subscription_level=User.SUBSCRIPTION_PRO  # 使用Pro版本以测试付费功能
        )
        
        self.room = Room.objects.create(
            room_code='TEST01',
            host=self.user,
            status=RoomState.READY
        )
        
        # 创建一些初始环节
        RoomEventStep.objects.create(
            room=self.room,
            order=1,
            name='第一个环节',
            step_type=EventStep.STEP_FREE_CHAT,
            duration=300
        )
        
        RoomEventStep.objects.create(
            room=self.room,
            order=2,
            name='第二个环节',
            step_type=EventStep.STEP_GAME_PICTIONARY,
            duration=600
        )
        
        # 登录用户
        self.client.force_authenticate(user=self.user)
    
    def test_insert_step_api(self):
        """测试插入环节API"""
        url = f'/api/rooms/{self.room.room_code}/events/insert/'
        data = {
            'step_type': EventStep.STEP_PAUSE,
            'insert_order': 2,
            'name': '插入的暂停环节',
            'duration': 180,
            'configuration': {'message': '请稍等'}
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证环节已插入
        steps = self.room.event_steps.all().order_by('order')
        self.assertEqual(steps.count(), 3)
        
        # 验证顺序调整正确
        self.assertEqual(steps[0].order, 1)
        self.assertEqual(steps[0].name, '第一个环节')
        
        self.assertEqual(steps[1].order, 2)
        self.assertEqual(steps[1].name, '插入的暂停环节')
        self.assertEqual(steps[1].step_type, EventStep.STEP_PAUSE)
        
        self.assertEqual(steps[2].order, 3)
        self.assertEqual(steps[2].name, '第二个环节')
    
    def test_delete_step_api(self):
        """测试删除环节API"""
        step_to_delete = self.room.event_steps.get(order=2)
        url = f'/api/rooms/{self.room.room_code}/events/{step_to_delete.id}/'
        
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 验证环节已删除
        self.assertFalse(RoomEventStep.objects.filter(id=step_to_delete.id).exists())
        
        # 验证剩余环节的顺序调整正确
        remaining_step = self.room.event_steps.get()
        self.assertEqual(remaining_step.order, 1)
        self.assertEqual(remaining_step.name, '第一个环节')
    
    def test_permission_check(self):
        """测试权限检查"""
        # 创建另一个用户
        other_user = User.objects.create_user(
            username='otheruser',
            password='testpass123'
        )
        
        # 使用其他用户登录
        self.client.force_authenticate(user=other_user)
        
        # 尝试插入环节（应该失败）
        url = f'/api/rooms/{self.room.room_code}/events/insert/'
        data = {
            'step_type': EventStep.STEP_FREE_CHAT,
            'insert_order': 2,
            'name': '非法插入',
            'duration': 300
        }
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
